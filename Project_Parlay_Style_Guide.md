# Project Parlay Style Guide

This style guide defines the coding standards for all contributors to Project Parlay. Consistent code style improves readability, reduces bugs, and accelerates collaboration.

---

##  General Principles

- Write **readable**, **maintainable**, and **testable** code.
- All code must pass tests **and** code review by <PERSON><PERSON> before merging into production branches.

---

##  Whitespace Rules

Proper use of whitespace improves code readability.

### Indentation
- Use **4 spaces** per indentation level.
- **Never use tabs**. Use your editor’s settings to insert spaces.

### Blank Lines
- Separate top-level function and class definitions with **four blank lines**.
- Use **three blank line** between methods inside a class.
- Use **two blank lines to group related logic** in long functions or blocks.

### Inline Spacing
- Always use a single space after a comma, colon, or semicolon.
- No space directly inside parentheses, brackets, or braces.
- Use one space on each side of binary operators (e.g., `=`, `+`, `-`), but not for keyword arguments or default values:
  ```python
  x = 5
  my_func(x=5, y=3)
  ```


## Imports

- Imports should be on separate lines
- Group imports in the following order:
  1. Standard library
  2. Related third-party libraries
  3. Local application imports
- All imports should be at the top of the file that they are in


## Naming Conventions

- Functions and variables: lowercase_with_underscores
- Classes: CapitalizedWords
- Constants: UPPERCASE_WITH_UNDERSCORES
- Avoid single-letter variable names, except for counters like i, j, k


## Creating new Files and Folders

Depending on what team you're in,  you may have a designated folder to keep related code isolated. Our team-based file structure is as follows:

fullstack/
    frontend/
        src/
    backend/
        modules/
        data_science_modules/

- The data science team will be working out of the `fullstack/backenddata_science_modules` folder.
= The backend team will be working out of the `fullstack/backend` folder, and more specifically inside the `fullstack/backend/modules` folder.
- The frontend team will be working out of the `fullstack/frontend` folder, and more specifically inside the `fullstack/frontend/src` folder.

##  Commenting Standards

Good comments explain **why** as well as **what**. Clearly written code should already show what is happening at a basic level, so explain how it impacting the function and system as a small piece of a larger whole.

### Inline Comments
- These should be at the top of every seperated logic chunk in a function.
- Begin with a `#` followed by a **single space**.
- Place them above or to the right of the code they reference.

### Block Comments
- Use block comments to explain **non-obvious logic** or assumptions.
- Each line should start with a `#`, aligned vertically:
  ```python
  # This function pre-processes the input string by removing
  # all punctuation and converting it to lowercase for normalization.
  ```

### Docstrings
- Use triple-quoted strings ("""Docstring""") for:
  - Modules
  - Classes
  - Functions

  Docstrings are a breif paragraph that explains the purpose and logic implemented in a function or class. They should describe the inputs and outputs of the function as well. 
- Follow this structure:
  ```python
  def calculate_score(player):
      """
      Calculate the performance score for a player based on recent stats.

      Args:
          player (dict): A dictionary containing player stats.

      Returns:
          float: The calculated score.
      """
  ```

---

## Testing Before Submitting a Pull Request

All code **must** be tested before a pull request is submitted.

### Required Testing Steps

1. **Unit Tests**: Add or update unit tests for every new function, method, or class. Use `pytest` unless another framework is specified.
2. **Run Test Suite**: Run all tests locally with:
   ```bash
   pytest
   ```
   All tests must pass with no errors or warnings.

3. Test the base case for a function or class with at least 3 different data samples, and then test at least 3 edge cases for that function or class as well. Some examples of edge cases include gracefully handling bad input, input that may challange function logic, etc..

4. **Manual QA (if applicable)**: If your code impacts the UI or core workflows, validate it manually.

5. You must be prepared to answar the question in a code review meeting: **"Why are you very sure that this code works as intended"**, and answer with data driven analysis.
6. **Pre-Push Checklist**:
   - ✅ Code is Style Guide Compliant
   - ✅ Comments and docstrings are complete
   - ✅ All tests pass
   - ✅ No console debug print statements
   - ✅ Code is committed with a meaningful message

---

##  Pull Request Etiquette

- Name your branches descriptively and with your name (e.g.,`fix_database_typo-hartel`)
- Submit clear and concise PR titles (e.g., `Add support for multi-league predictions`)
- Tag relevant reviewers (Hartel, or Your Team Lead if you are a team member)
- Include links to related issues or tickets

---

## 👥 Collaboration Note

All contributors are expected to uphold these standards. If you’re unsure about a rule, ask in the team channel or open a draft PR for early feedback.

---

_This document is a living style guide and may be updated as the project evolves._
