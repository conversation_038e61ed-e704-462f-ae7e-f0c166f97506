import React, { useState } from "react";
import { HiBars3, HiTrash, HiPlus } from "react-icons/hi2";
import { IoShirtOutline } from "react-icons/io5";
import { useAuth } from "../../contexts/AuthContext";
import { usePicks } from "../../contexts/PicksContext";
import { getConfidenceColor } from "../../utils/colorUtils";

interface SubparlayPick {
  pID: string | number;
  odds: string;
  confidence?: number;
  name?: string;
  bayesian_conf?: number;
  bayesian_prob?: number;
  logistic_prob?: number;
  capital_limit?: number;
  event_id?: string;
  gameID?: number;
  league?: string;
  reusable?: boolean;
  stat_type?: string;
}

interface SubparlayColumn extends Array<SubparlayPick> {}

interface OptimizerData {
  best_score: number;
  split_index?: number;
  subparlays: SubparlayColumn[];
}

function HomePage() {
  const { navigateToView } = useAuth();
  const { getPicks, removePick, totalPicksCount } = usePicks();
  const userPicks = getPicks();

  // Optimizer state
  const [isOptimizing, setIsOptimizing] = useState(false);
  const [subparlays, setSubparlays] = useState<SubparlayColumn[]>([]);
  const [optimizerError, setOptimizerError] = useState<string>("");
  const [isParlaysExpanded, setIsParlaysExpanded] = useState(false);

  const API_BASE_URL = "/api";

  const handleAddPicksClick = () => {
    navigateToView("addPicks");
  };

  const handleOptimizeParlays = async () => {
    if (totalPicksCount < 2) {
      setOptimizerError("Need at least 2 picks to optimize parlays");
      return;
    }

    setIsOptimizing(true);
    setOptimizerError("");
    setSubparlays([]);

    try {
      // Load user picks from PicksContext into backend for optimization
      const loadResponse = await fetch(`${API_BASE_URL}/load_user_picks`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ picks: userPicks }),
      });

      if (!loadResponse.ok) {
        throw new Error(`Failed to load user picks: ${loadResponse.status}`);
      }

      const loadData = await loadResponse.json();
      console.log("Loaded user picks for optimization:", loadData.message);

      // Now run the optimizer
      const optimizeResponse = await fetch(`${API_BASE_URL}/optimize_split`);
      if (!optimizeResponse.ok) {
        throw new Error(`Failed to optimize: ${optimizeResponse.status}`);
      }

      const data: OptimizerData = await optimizeResponse.json();

      // Process subparlays with the same data structure
      const mappedSubparlays = (data.subparlays || []).map((subparlay: any[]) =>
        subparlay.map((pick: any) => ({
          ...pick,
          pID: pick.pID,
          name: pick.name, // Already formatted in backend
          confidence: pick.confidence,
          odds: pick.odds?.toString() || "1.85",
        }))
      );

      console.log("Subparlays with user data:", mappedSubparlays);
      setSubparlays(mappedSubparlays);

      // Auto-expand parlays section when optimization completes successfully
      setIsParlaysExpanded(true);

    } catch (error) {
      console.error("Error optimizing parlays:", error);
      setOptimizerError(
        `Error optimizing parlays: ${
          error instanceof Error ? error.message : String(error)
        }`
      );
    } finally {
      setIsOptimizing(false);
    }
  };

  return (
    <div className="min-h-screen bg-[#061844] text-[#58C612] flex flex-col items-center justify-center p-4 select-none">
      <div className="absolute top-15 left-10 sm:top-15 sm:left-15">
        <HiBars3 className="w-auto h-14 sm:h-16 text-white bg-[#233e6c] rounded-lg hover:text-white transition-colors duration-300 cursor-pointer" />
      </div>

      <div className="w-full h-[100px] flex justify-center mb-auto mt-5 mt-10">
        <header className="flex items-center justify-center">
          <img
            src="/project_parlay_logo.png"
            alt="Project Parlay Logo"
            className="w-auto h-32 sm:h-40 select-none transition-all duration-300 ease-in-out"
          />
        </header>
      </div>

      {totalPicksCount === 0 ? (
        // Empty state
        <div className="text-center mx-auto mb-auto mt-[-25%] sm:mt-[-10%] ">
          <h2 className="text-white text-[400%] font-bold text-center">
            Time for a fresh start
          </h2>

          <p className="text-white text-[125%] sm:text-[200%] mb-8">
            You don't have any picks in your list
          </p>

          <button
            onClick={handleAddPicksClick}
            className="px-8 py-4 bg-[#233e6c] hover:bg-[#232d6c] text-white w-[60%] font-bold rounded-lg text-[100%] text-[150%] sm:text-[200%] transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer"
          >
            Add Picks
          </button>
        </div>
      ) : (
        // My Picks display
        <div className="w-full max-w-7xl mx-auto px-4 mb-auto mt-4">
          <div className="mb-6">
            <h2 className="text-white text-3xl sm:text-4xl font-bold">
              My Picks ({totalPicksCount})
            </h2>
          </div>

          {/* 2-column grid of picks */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {userPicks.map((pick) => (
              <div
                key={pick.id}
                className="bg-[#233e6c] rounded-xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 ease-linear hover:cursor-pointer hover:scale-[101.5%]"
              >
                <div className="flex flex-row gap-6 my-auto justify-center items-center">
                  {/* Player info */}
                  <div className="flex flex-col items-center md:flex-1 min-w-0">
                    <div
                      className="w-32 h-32 rounded-full mb-2 flex items-center justify-center bg-gradient-to-b from-gray-800 to-gray-900 relative overflow-hidden"
                      style={{
                        border: `4px solid ${getConfidenceColor(pick.confidence || 75)}`,
                      }}
                    >
                      <IoShirtOutline
                        className="w-20 h-20 absolute"
                        style={{ color: getConfidenceColor(pick.confidence || 75) }}
                      />
                      <div className="text-white font-bold text-lg sm:text-xl z-10 relative">
                        {pick.playerNumber}
                      </div>
                    </div>
                    <h3 className="font-bold text-lg text-center">
                      {pick.playerName}
                    </h3>
                    <p className="text-sm text-center text-white">{pick.betType}</p>
                    <p className="text-xs text-gray-400 text-center">
                      {pick.gameInfo}
                    </p>
                    {pick.handicapperName && (
                      <p className="text-blue-400 text-xs mt-1 text-center">
                        Recommended by {pick.handicapperName}
                      </p>
                    )}
                  </div>

                  {/* Confidence and remove button */}
                  <div className="flex flex-col items-center gap-2 md:flex-1 min-w-0 m-auto h-full">
                    <div className="text-center flex flex-col items-center justify-start h-full">
                      <div
                        className="text-[75px] md:text-[100px] font-bold mt-[-24px]"
                        style={{ color: getConfidenceColor(pick.confidence || 75) }}
                      >
                        {pick.confidence || 75}
                      </div>
                      <div className="text-[24px] md:text-[24px] font-bold text-white mt-[-24px]">
                        Confidence
                        <br />
                        Rating
                      </div>
                    </div>
                    <button
                      onClick={() => removePick(pick.id)}
                      className="p-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-all duration-300 hover:scale-105 shadow-md hover:shadow-lg hover:cursor-pointer"
                      title="Remove pick"
                    >
                      <HiTrash className="w-8 h-8 hover:cursor-pointer" />
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Action Buttons Container */}
          <div className="mt-8 relative">
            {/* Centered Optimize Parlays button */}
            <div className="flex justify-center">
              <button
                onClick={handleOptimizeParlays}
                disabled={isOptimizing || totalPicksCount < 2}
                className={`px-12 py-6 font-bold rounded-lg text-2xl transition-all ease-linear duration-300 shadow-lg hover:shadow-xl ${
                  isOptimizing || totalPicksCount < 2
                    ? 'bg-gray-600 text-gray-400 cursor-not-allowed'
                    : 'bg-[#233e6c] hover:bg-[#1a2d54] text-white hover:cursor-pointer'
                }`}
              >
                {isOptimizing ? 'Optimizing...' : 'Optimize Parlays'}
              </button>
            </div>

            {/* Right-aligned Add More Picks button */}
            <button
              onClick={handleAddPicksClick}
              className="absolute top-0 right-0 px-6 py-3 bg-[#233e6c] hover:bg-[#1a2d54] text-white font-bold rounded-lg text-lg transition-all ease-linear duration-300 shadow-lg hover:shadow-xl hover:cursor-pointer flex items-center gap-2"
            >
              <HiPlus className="w-5 h-5" />
              Add More Picks
            </button>
          </div>

          {/* Error Display */}
          {optimizerError && (
            <div className="mt-8 p-4 bg-red-900 bg-opacity-50 border border-red-500 rounded-xl">
              <p className="text-red-400 text-center">
                <strong>Error:</strong> {optimizerError}
              </p>
            </div>
          )}

          {/* Generated Parlays Section */}
          {subparlays.length > 0 && (
            <div className="mt-8">
              <div
                className="bg-[#233e6c] rounded-t-xl p-6 cursor-pointer flex items-center justify-between hover:bg-[#1a2d54] transition-colors duration-300"
                onClick={() => setIsParlaysExpanded(!isParlaysExpanded)}
              >
                <div className="flex items-center gap-3">
                  <span
                    className={`text-white text-2xl font-bold transition-transform duration-200 ${
                      isParlaysExpanded ? 'rotate-180' : ''
                    }`}
                  >
                    ▼
                  </span>
                  <div>
                    <h3 className="text-white text-2xl font-bold">Generated Parlays</h3>
                    <p className="text-white/70 text-sm mt-1">
                      {subparlays.length} optimized parlay{subparlays.length !== 1 ? 's' : ''} ready
                    </p>
                  </div>
                </div>
                <div className="text-white/60 text-sm">
                  {isParlaysExpanded ? 'Click to collapse' : 'Click to expand'}
                </div>
              </div>

              {isParlaysExpanded && (
                <div className="bg-[#233e6c] rounded-b-xl p-6 overflow-visible">
                  <div className="flex justify-center gap-8 overflow-visible">
                    {subparlays.map((column, i) => (
                      <div key={i} className="flex flex-col items-center overflow-visible">
                        <div className="text-white text-lg font-bold mb-4">
                          Parlay {i + 1}
                        </div>
                        <div className="flex flex-col gap-3 min-w-[160px] overflow-visible">
                          {column.map((pick) => (
                            <div
                              key={pick.pID}
                              className="h-20 w-full rounded-lg border-2 border-white/30 cursor-pointer transition-all duration-300 hover:scale-105 hover:shadow-lg hover:border-white/60 relative group overflow-visible"
                              style={{
                                backgroundColor: getConfidenceColor(pick.confidence),
                              }}
                            >
                              {/* Pick content */}
                              <div className="p-3 h-full flex flex-col justify-center items-center text-center">
                                <div className="text-black font-semibold text-base leading-tight">
                                  {pick.name?.split(' - ')[0] || `Pick #${pick.pID}`}
                                </div>
                                <div className="text-black/80 text-xs mt-1">
                                  {pick.confidence?.toFixed(0)}% confidence
                                </div>
                              </div>

                              {/* Comprehensive hover tooltip */}
                              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-4 py-3 bg-gray-900 text-white text-sm rounded-lg shadow-xl opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-[9999] min-w-[280px]">
                                <div className="space-y-2">
                                  {/* Player and Bet Info */}
                                  <div className="border-b border-gray-600 pb-2">
                                    <div className="font-bold text-green-400">{pick.name || `Pick #${pick.pID}`}</div>
                                    <div className="text-gray-300 text-xs">{pick.league} • {pick.stat_type}</div>
                                  </div>

                                  {/* Confidence and Odds */}
                                  <div className="grid grid-cols-2 gap-3">
                                    <div>
                                      <div className="text-gray-400 text-xs">Confidence</div>
                                      <div className="font-semibold">{pick.confidence?.toFixed(0)}%</div>
                                    </div>
                                    <div>
                                      <div className="text-gray-400 text-xs">Odds</div>
                                      <div className="font-semibold">{pick.odds}</div>
                                    </div>
                                  </div>

                                  {/* Probability Analysis */}
                                  <div className="border-t border-gray-600 pt-2">
                                    <div className="text-gray-400 text-xs mb-1">Probability Analysis</div>
                                    <div className="grid grid-cols-2 gap-2 text-xs">
                                      <div>
                                        <span className="text-gray-400">Bayesian:</span> {pick.bayesian_prob ? (pick.bayesian_prob * 100).toFixed(1) : 'N/A'}%
                                      </div>
                                      <div>
                                        <span className="text-gray-400">Logistic:</span> {pick.logistic_prob ? (pick.logistic_prob * 100).toFixed(1) : 'N/A'}%
                                      </div>
                                    </div>
                                  </div>

                                  {/* Additional Info */}
                                  <div className="border-t border-gray-600 pt-2 text-xs space-y-1">
                                    <div className="text-gray-400">Event ID: <span className="text-gray-300">{pick.event_id || 'N/A'}</span></div>
                                    <div className="text-gray-400">Bayesian Conf: <span className="text-gray-300">{pick.bayesian_conf?.toFixed(2) || 'N/A'}</span></div>
                                    {pick.gameID && pick.gameID !== -1 && (
                                      <div className="text-gray-400">Game ID: <span className="text-gray-300">{pick.gameID}</span></div>
                                    )}
                                    {pick.capital_limit !== undefined && (
                                      <div className="text-gray-400">Capital Limit: <span className="text-gray-300">{pick.capital_limit}</span></div>
                                    )}
                                    {pick.reusable !== undefined && (
                                      <div className="text-gray-400">Reusable: <span className="text-gray-300">{pick.reusable ? 'Yes' : 'No'}</span></div>
                                    )}
                                  </div>
                                </div>

                                {/* Tooltip arrow */}
                                <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900"></div>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      )}


    </div>
  );
}

export default HomePage;
